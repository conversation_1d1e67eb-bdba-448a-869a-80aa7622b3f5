import {
  CommonActions,
  createNavigationContainerRef,
} from '@react-navigation/native';
// import Home from '../Screen/Page/Home';

export enum RootScreen {
  splashView = 'Splash',
  listCoursebyCate = 'ListCoursebyCate',
  myCourses = 'myCourses',
  Instructors = 'Instructors',
  CourseDetail = 'CourseDetail',
  DetailNews = 'DetailNews',
  navigateESchoolParent = 'navigateESchoolParent',
  navigateESchoolView = 'navigateESchoolView',
  navigateCommunityParent = 'navigateCommunityParent',
  navigateCommunityView = 'navigateCommunityView',
  navigateSakupiParent = 'navigateSakupiParent',
  navigateSakupiView = 'navigateSakupiView',
  DrawerNavigation = 'DrawerNavigation',
  Test = 'Test',
  OverviewTest = 'OverviewTestNew',
  ProccessCourse = 'ProccessCourse',
  doingTestNew = 'doingTestNew',
  doingTest = 'doingTest',
  tryingHistoryTest = 'TryingHistoryTest',
  tryingTests = 'TryingTests',
  historyTryingList = 'HistoryTryingList',
  resultTest = 'resultTest',
  resultTestNew = 'resultTestNew',
  login = 'Login',
  ProfileRankScreen = 'ProfileRankScreen',
  SettingProfile = 'SettingProfile',
  BiometricSetting = 'BiometricSetting',
  ForgotPass = 'ForgotPass',
  order = 'ORDER',
  LearnCourse = 'LearnCourse',
  IntroductionLesson = 'IntroductionLesson',
  CreateFlashCard = 'CreateFlashCard',
  VnpayPaymentScreen = 'VnpayPaymentScreen',
  PurchaseHistory = 'PurchaseHistory',
  CoursebyTopic = 'CoursebyTopic',
  //
  // Groups
  CommunityLayout = 'CommunityLayout',
  GroupIndex = 'GroupIndex',
  SocialGroups = 'SocialGroups',
  AllGroupsLoadMore = 'AllGroupsLoadMore',
  PostDetail = 'PostDetail',
  Certificate = 'Certificate',
  PolicyView = 'PolicyView',
  FAQView = 'FAQView',
  ProfileCommunity = 'ProfileCommunity',
  // notification
  Notification = 'Notification',
  NotifCommunity = 'NotifCommunity',
  createPost = 'createPost',
  // Quiz
  DeckDetail = 'DeckDetail',
  FlashcardMode = 'FlashcardMode',
  StartQuizGame = 'StartQuizGame',
  StartQuizGameFlascard = 'StartQuizGameFlascard',
  QuizGame = 'QuizGame',
  HomeGame = 'HomeGame',
  StartALTP = 'StartALTP',
  ResultALTP = 'ResultALTP',
  // Game Ranking
  GameRanking = 'GameRanking',
  // Saved Posts
  SavedPosts = 'SavedPosts',

  // Game
  StartSakuTB = 'StartSakuTB',

  StartSakuLC = 'StartSakuLC',

  StartMGHH = 'StartMGHH',

  StartDHBC = 'StartDHBC',

  StartSakuXT = 'StartSakuXT',
}

export const navigationRef = createNavigationContainerRef();
export function navigate(routeName: string, params?: object) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(CommonActions.navigate(routeName, params));
  }
}

export function navigateBack() {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(CommonActions.goBack());
  }
}
export function navigateBackWithParams(screenName: string, params?: any) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.navigate({
        name: screenName,
        params: params,
      }),
    );
  }
}
export function navigateReset(routeName: string, params?: object) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{name: routeName, params}],
      }),
    );
  }
}

// const Stack = createNativeStackNavigator();

// export default function Router() {
//   return (
//     <Stack.Navigator screenOptions={{headerShown: false}}>
//       <Stack.Screen
//         name={RootScreen.navigateESchoolView}
//         component={EschoolLayout}
//       />

//       <Stack.Screen name={RootScreen.CourseDetail} component={CourseDetail} />
//       <Stack.Screen name={RootScreen.DetailNews} component={DetailNews} />
//       <Stack.Screen name={RootScreen.Instructors} component={Instructors} />
//       <Stack.Screen name={RootScreen.Test} component={OverviewTest} />
//       <Stack.Screen name={RootScreen.doingTest} component={DoingTest} />
//       <Stack.Screen
//         name={RootScreen.tryingHistoryTest}
//         component={HistoryTryingTest}
//       />
//       <Stack.Screen name={RootScreen.tryingTests} component={TryingTests} />
//       <Stack.Screen
//         name={RootScreen.historyTryingList}
//         component={HistoryTryingList}
//       />
//       <Stack.Screen name={RootScreen.resultTest} component={ResultTest} />
//       <Stack.Screen name={RootScreen.order} component={Order} />
//       <Stack.Screen name={RootScreen.LearnCourse} component={LearnCourse} />
//       <Stack.Screen
//         name={RootScreen.ProccessCourse}
//         component={ProccessCourseDetail}
//       />
//       <Stack.Screen
//         name={RootScreen.listCoursebyCate}
//         component={ListCoursebyCate}
//       />
//       <Stack.Screen name={RootScreen.myCourses} component={ListCoursebyCate} />
//       <Stack.Screen
//         name={RootScreen.SettingProfile}
//         component={SettingProfile}
//       />
//       <Stack.Screen
//         name={RootScreen.CreateFlashCard}
//         component={CreateFlashCard}
//       />

//       <Stack.Screen name={RootScreen.SocialGroups} component={SocialGroups} />

//       <Stack.Screen name={RootScreen.QuizGame} component={QuizGameHome} />
//       <Stack.Screen name={RootScreen.StartQuizGame} component={QuizScreen} />
//       <Stack.Screen
//         name={RootScreen.StartQuizGameFlascard}
//         component={QuizScreenFlashcard}
//       />
//       {/* flashcard */}
//       <Stack.Screen name={RootScreen.DeckDetail} component={DeckDetailScreen} />
//       <Stack.Screen
//         name={RootScreen.FlashcardMode}
//         component={FlashcardModeScreen}
//       />
//     </Stack.Navigator>
//   );
// }
