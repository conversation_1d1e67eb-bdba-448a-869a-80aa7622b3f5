import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
  Image,
  ImageBackground,
} from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../../../redux/store/store';
import { Winicon } from 'wini-mobile-components';

const { width, height } = Dimensions.get('window');

interface MoneyLevelModalProps {
  visible: boolean;
  onClose: () => void;
}

const MoneyLevelModal = ({ visible, onClose }: MoneyLevelModalProps) => {
  // Lấy dữ liệu từ Redux store
  const gameState = useSelector((state: RootState) => state.game);
  const { moneyLevels, currentQuestionIndex } = gameState;

  // Kiểm tra xem câu hỏi hiện tại có phải là mốc quan trọng không (câu 5, 10, 15)
  const isMilestone = (index: number) => {
    return index === 4 || index === 9 || index === 14; // Câu 5, 10, 15 (index từ 0)
  };

  // Render từng mức tiền
  const renderMoneyLevels = () => {
    if (!moneyLevels || moneyLevels.length === 0) {
      return <Text style={styles.loadingText}>Đang tải...</Text>;
    }

    // Đảo ngược mảng để hiển thị từ cao đến thấp
    const reversedLevels = [...moneyLevels].reverse();

    return reversedLevels.map((level, index) => {
      const actualIndex = moneyLevels.length - 1 - index; // Chuyển đổi index để khớp với currentQuestionIndex
      const questionNumber = actualIndex + 1; // Số câu hỏi (từ 1 đến 15)

      // Kiểm tra xem câu hỏi này đã được trả lời chưa
      const isAnswered = actualIndex < currentQuestionIndex;

      // Kiểm tra xem câu hỏi hiện tại đang được trả lời
      const isCurrent = actualIndex === currentQuestionIndex;

      // Xác định style dựa trên trạng thái
      const containerStyle = [
        styles.levelContainer,
        isMilestone(actualIndex) && styles.milestoneContainer,
        isCurrent && styles.currentContainer,
        isAnswered && styles.answeredContainer, // Highlight tất cả câu hỏi đã trả lời
      ];

      const textStyle = [
        styles.levelNumber,
        (isCurrent || isAnswered) && styles.currentText, // Highlight text cho cả câu hiện tại và câu đã trả lời
      ];

      const moneyStyle = [
        styles.moneyText,
        (isCurrent || isAnswered) && styles.currentText, // Highlight text cho cả câu hiện tại và câu đã trả lời
      ];

      return (
        <View key={actualIndex} style={containerStyle}>
          <View style={styles.numberContainer}>
            <Text style={textStyle}>
              {questionNumber < 10 ? `0${questionNumber}` : questionNumber}
            </Text>
          </View>
          <View style={styles.moneyContainer}>
            <Image
              source={isAnswered ? require('../assets/coin-icon.png') : require('../assets/coin-icon.png')}
              style={styles.coinIcon}
            />
            <Text style={moneyStyle}>{level.toLocaleString()}</Text>
          </View>
          {isMilestone(actualIndex) && (
            <View style={styles.milestoneIconContainer}>
              <Image
                source={require('../assets/gift.png')}
                style={styles.milestoneIcon}
              />
            </View>
          )}
        </View>
      );
    });
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          {/* <ImageBackground
            source={require('../assets/modal_bg.png')}
            style={styles.modalBackground}
            resizeMode="stretch"
          > */}
            <View style={styles.headerContainer}>
              <Text style={styles.headerText}>Bảng Điểm</Text>
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Text style={styles.closeButtonText}>×</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.levelsContainer}>
              {renderMoneyLevels()}
            </View>
          {/* </ImageBackground> */}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.92,
    backgroundColor: 'rgba(176, 140, 99, 0.96)', // Màu nền nâu như trong ảnh
    height: height *0.96,
    borderRadius: 15,
    overflow: 'hidden',
  },
  modalBackground: {
    width: '100%',
    height: '100%',
    paddingVertical: 20,
    paddingHorizontal: 15,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  headerText: {
    color: '#FFD700',
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
    marginTop: 10,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  levelsContainer: {
    flex: 1,
    paddingHorizontal: 10,
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginVertical: 2,
    height: 40,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  milestoneContainer: {
    backgroundColor: 'rgba(255, 240, 120, 0.3)',
  },
  currentContainer: {
    backgroundColor: 'rgba(255, 240, 120, 0.5)',
  },
  answeredContainer: {
    backgroundColor: 'rgba(255, 240, 120, 0.5)', // Màu xanh lá cây mờ để thể hiện câu hỏi đã trả lời
  },
  numberContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(100, 100, 150, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  levelNumber: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  moneyContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  coinIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  moneyText: {
    color: '#a0a0ff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  currentText: {
    color: 'white',
    fontWeight: 'bold',
  },
  milestoneIconContainer: {
    marginLeft: 10,
  },
  milestoneIcon: {
    width: 24,
    height: 24,
  },
  loadingText: {
    color: 'white',
    textAlign: 'center',
    padding: 20,
  },
});

export default MoneyLevelModal;
