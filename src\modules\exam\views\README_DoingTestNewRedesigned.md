# DoingTestNewRedesigned - <PERSON><PERSON><PERSON> hình thi mới được thiết kế lại

## Tổng quan

File `doingTestNewRedesigned.tsx` là phiên bản được thiết kế lại hoàn toàn của màn hình thi, đ<PERSON><PERSON> <PERSON><PERSON> các yêu cầu:

1. **Thêm Section Selector**: Horizontal scroll để chọn phần thi (chỉ hiển thị khi có > 1 section)
2. **Smart Exam Navigation**: Logic thông minh cho điều hướng bài thi:
   - 1 bài: Không hiển thị nút navigation
   - Bà<PERSON> đầu: Chỉ hiển thị nút "Next"
   - Bài giữa: Hiể<PERSON> thị cả "Previous" và "Next"
   - Bài cuối: Chỉ hiển thị nút "Previous"
3. **Scroll View thay vì Page View**: Hiển thị tất cả câu hỏi trong một danh sách scroll
4. **Load dữ liệu theo section/exam**: <PERSON><PERSON><PERSON> ưu hiệu suất
5. **UI tối ưu**: Bố cục hiển thị tốt hơn với style cải tiến

## Cấu trúc chính

### 1. State Management
```typescript
const [sections, setSections] = useState<any[]>([]);
const [exams, setExams] = useState<any[]>([]);
const [currentQuestions, setCurrentQuestions] = useState<any[]>([]);
const [selectedSection, setSelectedSection] = useState<any>(null);
const [selectedExam, setSelectedExam] = useState<any>(null);
const [currentExamIndex, setCurrentExamIndex] = useState(0);
```

### 2. Các chức năng chính

#### Load dữ liệu ban đầu
- Load test data với sections và exams
- Tự động chọn section và exam đầu tiên
- Load câu hỏi cho exam được chọn

#### Section Selection
- Hiển thị danh sách sections dạng horizontal scroll
- Khi chọn section mới, load exams của section đó
- Tự động chọn exam đầu tiên trong section

#### Smart Exam Navigation
- **Logic thông minh**: Chỉ hiển thị nút cần thiết
  - 1 bài: Không có nút navigation
  - Bài đầu (1/n): Chỉ nút "Next"
  - Bài giữa (x/n): Cả "Previous" và "Next"
  - Bài cuối (n/n): Chỉ nút "Previous"
- **Tiêu đề động**: Hiển thị tên exam thay vì "Bài 1/3"
- **Indicator**: Hiển thị vị trí hiện tại (1/3) bên dưới tên exam

#### Question Display
- Hiển thị tất cả câu hỏi trong ScrollView
- Mỗi câu hỏi có header riêng với số thứ tự
- Support cả single choice và multiple choice
- Hiển thị HTML content cho câu hỏi

### 3. UI Components

#### Header
- Timer đếm ngược
- Tên bài thi hiện tại
- Nút Submit

#### Section & Exam Selector
- **Clean Section Display**: Chỉ hiển thị khi có > 1 section, không có text label
- **Enhanced Styling**:
  - Rounded corners với shadow effect
  - Active state với primary color
  - Smooth transitions
- **Responsive Navigation**: Buttons tự động ẩn/hiện theo logic
- **Centered Layout**: Exam title căn giữa với indicator bên dưới

#### Questions List
- Card-based design cho mỗi câu hỏi
- Color coding cho answers đã chọn
- Support HTML rendering

#### Clean Layout
- Không có footer để tối ưu không gian hiển thị
- Focus hoàn toàn vào nội dung câu hỏi

## Cách sử dụng

### 1. Import và sử dụng
```typescript
import DoingTestNewRedesigned from './doingTestNewRedesigned';

// Sử dụng trong navigation
navigation.navigate('DoingTestNewRedesigned', {
  testId: 'your-test-id'
});
```

### 2. Props cần thiết
- `testId`: ID của test cần load

### 3. API Dependencies
Component sử dụng các DataController:
- `Exam`: Load exam data
- `Answer`: Load answer data
- `Test`: Load test data
- `Section`: Load section data

## Tính năng mới

### 1. Section-based Organization
- Tổ chức câu hỏi theo sections
- Dễ dàng chuyển đổi giữa các phần thi
- Load data theo demand

### 2. Exam Navigation
- Điều hướng giữa các bài thi trong cùng section
- Hiển thị progress rõ ràng
- Tự động load câu hỏi khi chuyển bài

### 3. Improved UX
- Scroll view cho phép xem tất cả câu hỏi
- Visual feedback tốt hơn
- Progress tracking chi tiết

### 4. Performance Optimization
- Load data theo section/exam
- Không load tất cả data cùng lúc
- Efficient state management

## Translation Keys mới

Đã thêm các key translation mới:
```json
{
  "exam": {
    "selectSection": "Chọn phần thi",
    "exam": "Bài thi",
    "question": "Câu hỏi",
    "previousExam": "Bài trước",
    "nextExam": "Bài tiếp theo",
    "questionsAnswered": "câu đã trả lời",
    "reviewAnswers": "Xem lại đáp án",
    "submitExam": "Nộp bài thi"
  },
  "common": {
    "loading": "Đang tải...",
    "add": "Thêm",
    "refresh": "Làm mới"
  }
}
```

## So sánh với phiên bản cũ

| Tính năng | Phiên bản cũ | Phiên bản mới |
|-----------|--------------|---------------|
| Navigation | PagerView (page by page) | ScrollView (tất cả câu hỏi) |
| Organization | Flat list | Section-based |
| Data Loading | Load tất cả | Load theo demand |
| Exam Selection | Không có | Navigation buttons |
| UI Layout | Single page focus | Overview toàn bộ |
| Performance | Load nhiều data | Tối ưu loading |

## Lưu ý khi triển khai

1. **Backup**: Giữ lại file cũ để fallback nếu cần
2. **Testing**: Test kỹ với nhiều loại data khác nhau
3. **API**: Đảm bảo API trả về đúng cấu trúc section/exam
4. **Translation**: Kiểm tra tất cả translation keys
5. **Performance**: Monitor performance với data lớn

## Roadmap

### Phase 1 (Hiện tại)
- ✅ Basic UI redesign
- ✅ Section/Exam navigation
- ✅ ScrollView implementation
- ✅ Translation support

### Phase 2 (Tương lai)
- [ ] Submit functionality
- [ ] Review modal
- [ ] Offline support
- [ ] Advanced filtering
- [ ] Bookmark questions
