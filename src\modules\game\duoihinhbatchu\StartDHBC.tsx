import React, {useEffect, useRef, useState, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Image,
  ActivityIndicator,
} from 'react-native';
import CountBadge from '../components/CountQuestions';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {useDhbcHook} from '../../../redux/hook/game/dhbcHook';
import {SafeAreaView} from 'react-native-safe-area-context';
import {CardTitleGame} from '../components/CardTitleGame';
import {BottomGame} from '../components/BottomGame';
import ModelConfirm from '../components/ModelConfirm';
// import HintModel from '../components/HintModel'; // Comment: Temporarily disabled
import GameOverModal from '../components/GameOverModel';
import NoDataScreen from './components/NoDataScreen';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useNavigation, useRoute} from '@react-navigation/native';
import WinnerModal from './components/WinnerModal';

const GameScreen = () => {
  const dhbcHook = useDhbcHook();
  const gameHook = useGameHook();
  const navigation = useNavigation<any>();
  const route = useRoute<any>();

  // Get route params
  const {competenceId = '5', milestoneId = 1} = route.params || {};

  const {totalLives, currentLives, isGameOver, messageGameOver} =
    useSelector((state: RootState) => state.gameStore);
  const {
    totalQuestion,
    questionDone,
    currentQuestion,
    
    loading,
    configLoading,
    error,
    configError,
    initialized,
    configInitialized,
    noData,
    gameConfig
  } = useSelector((state: RootState) => state.dhbcStore);

  const [isShowKeyboard, setIsShowKeyboard] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [isCorrect, setIsCorrect] = useState<boolean>(false);
  const [answer, setAnswer] = useState<string>('');
  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);
  const [isWinLevel, setIsWinLevel] = useState<boolean>(false);
  const hiddenInputRef = useRef<TextInput | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Define functions first
  const showKeyboard = () => {
    if (hiddenInputRef.current) {
      hiddenInputRef.current.focus();
    }
  };

  const startGame = useCallback(() => {
    resetQuestion();

    // Sử dụng config từ API nếu có, fallback về default values
    if (gameConfig) {
      gameHook.setData({stateName: 'totalLives', value: gameConfig.maxLives});
      gameHook.setData({stateName: 'currentLives', value: gameConfig.maxLives});
      gameHook.setData({stateName: 'time', value: gameConfig.timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});
      gameHook.setData({stateName: 'isGameOver', value: false});
    } else {
      // Fallback to default values
      gameHook.restartGame();
    }

    dhbcHook.startGame();
  }, [gameConfig, dhbcHook, gameHook]);


  // Load game data on component mount
  useEffect(() => {
    console.log('[StartDHBC] Loading game config and questions...');

    // Load game config first
    dhbcHook.loadGameConfig(ConfigAPI.gameDHBC);

    // Load game questions
    dhbcHook.loadQuestions(ConfigAPI.gameDHBC, milestoneId, competenceId);

    // Setup keyboard listeners
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setIsShowKeyboard(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setIsShowKeyboard(false);
      hiddenInputRef.current?.blur();
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
      // Cleanup timeout on unmount
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [competenceId, milestoneId]);

  // Start game when data is loaded
  useEffect(() => {
    if (initialized && configInitialized && !loading && !configLoading) {
      if (!noData) {
        startGame();
      }
    }
  }, [initialized, configInitialized, loading, configLoading]);

  useEffect(() => {
    if (currentLives === 0) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);

  // Thua
  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };


  // Reset câu hỏi
  const resetQuestion = () => {
    setIsCorrect(false);
    setIsError(false);
    setAnswer('');
  };

  // Kiểm tra đáp án
  const checkAnswer = () => {
    setIsError(false);
    setIsCorrect(false);

    // Null check cho currentQuestion và answer
    if (!currentQuestion || !currentQuestion.answer) {
      console.warn('[StartDHBC] Current question or answer is missing');
      return;
    }

    if (answer.toLowerCase() === currentQuestion.answer.toLowerCase()) {
      setIsCorrect(true);

      // Clear previous timeout if exists
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout with cleanup
      timeoutRef.current = setTimeout(() => {
        resetQuestion();
        dhbcHook.setData({stateName: 'questionDone', value: questionDone + 1});
        dhbcHook.nextQuestion();
        timeoutRef.current = null;
      }, 2000);
    } else {
      setIsError(true);
      gameHook.setData({stateName: 'currentLives', value: currentLives - 1});
    }
  };

  // Sử dụng gợi ý - Comment: Tạm thời disable vì không có hint từ API
  // const useHint = () => {
  //   gameHook.setData({stateName: 'gem', value: gem - 10});
  //   setShowModelConfirm(false);
  //   // setShowHintModel(true); // Comment: Tạm thời comment
  //   console.log('Hint feature temporarily disabled - no hint data from API');
  // };

  // Retry loading data
  const retryLoadData = () => {
    console.log('[StartDHBC] Retrying to load data...');
    dhbcHook.loadGameConfig(ConfigAPI.gameDHBC);
    dhbcHook.loadQuestions(ConfigAPI.gameDHBC, milestoneId, competenceId);
  };

  // Go back to previous screen
  const goBack = () => {
    navigation.goBack();
  };

  // Show loading screen
  if (loading || configLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <View style={styles.loadingContent}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <Text style={styles.loadingText}>Đang tải dữ liệu game...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show no data screen
  if (noData || error || configError) {
    return (
      <NoDataScreen
        message={error || configError || 'Không có dữ liệu câu hỏi cho game này'}
        onRetry={retryLoadData}
        onGoBack={goBack}
      />
    );
  }

  return (
    <SafeAreaView style={{flex: 1}}>
      <View style={styles.container}>
        {/* Header */}
        <HeadGame
          isShowSuggest={true}
          onUseHint={() => setShowModelConfirm(true)}
          timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
          gameId={ConfigAPI.gameDHBC}
        />
        {!isShowKeyboard ? (
          <View>
            <LineProgressBar
              progress={totalQuestion > 0 ? (questionDone / totalQuestion) * 100 : 0}></LineProgressBar>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Lives
                totalLives={totalLives}
                currentLives={currentLives}></Lives>
              <CountBadge
                current={questionDone}
                total={totalQuestion}></CountBadge>
            </View>
            <View style={{marginTop: 16}}>
              <CardTitleGame title={currentQuestion.text || ''}></CardTitleGame>
            </View>
          </View>
        ) : null}
        {/* Game Content */}
        <View style={styles.gameContent}>
          {/* Emoji Game Card */}
          <Image
            source={{
              uri: currentQuestion?.image || '',
            }}
            style={styles.imageContainer}
            resizeMode="cover"
            onError={(imageError) => {
              console.warn('[StartDHBC] Image load error:', imageError.nativeEvent.error);
            }}
          />

          {/* Answer */}
          <View
            style={[
              styles.answerContainer,
              isError ? styles.answerContainerError : null,
              isCorrect ? styles.answerContainerCorrect : null,
            ]}>
            {answer.length < 1 ? (
              <Text style={styles.placeholderText}>
                Nhập đáp án tại đây
              </Text>
            ) : (
              <Text style={styles.answerText}>{answer}</Text>
            )}
            {isCorrect && (
              <Text style={styles.correctText}>Đáp án chính xác</Text>
            )}
            {isError && (
              <Text style={styles.errorText}>Sai rồi, hãy thử đáp án khác</Text>
            )}
            <TextInput
              ref={hiddenInputRef}
              style={styles.hiddenInput}
              value={answer}
              onChangeText={text => setAnswer(text)}
              autoCapitalize="none"
              autoCorrect={false}
              spellCheck={false}
              caretHidden={true}
            />
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.skipButton} onPress={showKeyboard}>
              <Text style={styles.skipButtonText}>Nhập đáp án</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.checkButton} onPress={checkAnswer}>
              <Text style={styles.checkButtonText}>Kiểm tra</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Bottom */}
        <View>
          <BottomGame
            resetGame={startGame}
            backGame={() => {navigation.goBack();}}
            pauseGame={() => {
              // xử lý pause game
              gameHook.pauseGame();
            }}
            volumeGame={() => {}}
          />
        </View>
      </View>
      <View style={styles.modalContainer}>
        {/* <ModelConfirm
          isShow={showModelConfirm}
          closeModal={() => setShowModelConfirm(false)}
          onConfirm={useHint}
        /> */}
        {/* Comment: Tạm thời comment hint vì không có field hint từ API */}
        {/* <HintModel
          isShow={showHintModel}
          closeModal={() => setShowHintModel(false)}
          text={currentQuestion.hint}
        /> */}
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={startGame}
          message={messageGameOver}
          isTimeOut={false}
        />
        {/* <ModelDoneLevel
          visible={isWinLevel}
          onNextLevel={startGame}
          currentGem={gem - 30}
          currentCup={cup - 10}
          gemAdd={30}
          cupAdd={10}
        /> */}
        <WinnerModal
          visible={isWinLevel}
          onClose={() => {
            setIsWinLevel(false);
          }}
          restartGame={startGame}
          currentLives={currentLives}
          competenceId={competenceId}
          gameId={ConfigAPI.gameDHBC}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 16,
  },
  gameContent: {
    marginTop: 32,
    flex: 1,
    alignItems: 'center',
  },
  // Loading styles
  loadingContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  // Placeholder text style
  placeholderText: {
    color: '#999',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
  },
  // Modal container style
  modalContainer: {
    zIndex: 1000,
  },

  imageContainer: {
    width: '90%',
    height: 200,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
  },

  answerContainer: {
    position: 'relative',
    marginTop: 32,
    maxWidth: '70%',
    minWidth: 200,
    backgroundColor: 'white',
    borderRadius: 15,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  answerContainerError: {
    borderColor: '#FF6B6B',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerContainerCorrect: {
    borderColor: '#2EB553',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',

    backgroundColor: '#FCF8E8',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginTop: 5,
  },
  correctText: {
    fontSize: 12,
    textAlign: 'center',

    backgroundColor: '#E8F8FC',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#2EB553',
    fontWeight: 'bold',
    marginTop: 5,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  skipButton: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 15,
    flex: 1,
    marginRight: 10,
  },
  skipButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  checkButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 15,
    flex: 1,
    marginLeft: 10,
  },
  checkButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  hiddenInput: {
    width: 0, // Chiều rộng 0
    height: 0, // Chiều cao 0
    opacity: 0, // Hoàn toàn trong suốt
    position: 'absolute', // Không chiếm không gian trong layout
    // Để đảm bảo nó thực sự không nhìn thấy và không tương tác ngoài ý muốn:
    top: -10000, // Đẩy ra rất xa màn hình
    left: -10000,
  },
});

export default GameScreen;
