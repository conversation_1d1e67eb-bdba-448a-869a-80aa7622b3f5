import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  ImageBackground,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import {AppButton, SkeletonImage, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {navigateBack} from '../../../router/router';
import {GameDA} from '../gameDA';
import ConfigAPI from '../../../Config/ConfigAPI';
import EmptyPage from '../../../Screen/emptyPage';
import { useRoute } from '@react-navigation/native';

const {width, height} = Dimensions.get('window');

// Interface cho dữ liệu ng<PERSON><PERSON><PERSON> ch<PERSON>i
interface Player {
  customerId: string;
  name: string;
  score: number;
  avatarUrl?: string;
}

const RankingScreen = () => {
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const gameDA = new GameDA();
  const route = useRoute<any>();
  const {gameId} = route.params || {gameId: 'ALTP'};

  useEffect(() => {
    fetchTopPlayers();
  }, []);

  const fetchTopPlayers = async () => {
    try {
      setLoading(true);
      const topPlayers = await gameDA.getTopPlayersbyGameId(10, gameId);
      setPlayers(topPlayers);
    } catch (error) {
      console.error('Error fetching top players:', error);
    } finally {
      setLoading(false);
    }
  };

  // Render avatar cho người chơi
  const renderAvatar = (player: Player, size: number) => {
    if (player.avatarUrl) {
      return (
        <Image
          key={player.avatarUrl}
          source={{
            uri: player.avatarUrl.includes('https')
              ? player.avatarUrl
              : `${ConfigAPI.urlImg + player.avatarUrl}`,
          }}
          style={[styles.avatar, {width: size, height: size}]}
        />
      );
    } else {
      return (
        <View
          style={[
            styles.avatarPlaceholder,
            {
              width: size,
              height: size,
              backgroundColor: ColorThemes.light.Primary_Color_Main,
            },
          ]}>
          <Text style={styles.avatarText}>
            {player.name ? player.name.charAt(0).toUpperCase() : 'U'}
          </Text>
        </View>
      );
    }
  };
  const coinImg = require('../ailatrieuphu/assets/coin-icon.png');

  // Render top 3 người chơi
  const renderTop3 = () => {
    return (
      <View style={styles.podiumContainer}>
        {/* Top 2 - Vị trí thứ 2 */}
        <View
          style={{
            ...styles.top2Container,
            flex: 1,
          }}>
          <View style={[styles.avatarContainer]}>
            {players.length > 1 ? (
              renderAvatar(players[1], 70)
            ) : (
              <View style={styles.emptyAvatarPlaceholder} />
            )}
          </View>
          <LinearGradient
            colors={['#FDAE38', '#FDAE38']}
            style={[styles.podiumColumn, styles.top2Container]}>
            <View style={styles.playerInfoContainer}>
              {players.length > 1 ? (
                <>
                  <Text style={styles.playerName}>{players[1].name}</Text>
                  <View style={styles.scoreContainer}>
                    <Image source={coinImg} style={styles.coinIcon} />
                    <Text style={styles.scoreText}>{players[1].score}</Text>
                  </View>
                  <View style={styles.rankContainer}>
                    <Text style={styles.rankNumberText}>2</Text>
                  </View>
                </>
              ) : (
                <View style={styles.rankContainer}>
                  <Text style={styles.rankNumberText}>2</Text>
                </View>
              )}
            </View>
          </LinearGradient>
        </View>

        {/* Top 1 - Vị trí thứ 1 */}
        <View style={{flex: 1, ...styles.podiumColumnFirst}}>
          <View style={[styles.avatarContainer]}>
            {players.length > 0 ? (
              <View>{renderAvatar(players[0], 90)}</View>
            ) : (
              <View style={styles.emptyAvatarPlaceholder} />
            )}
          </View>
          <LinearGradient
            colors={['#F75435', '#F75435']}
            style={[styles.podiumColumn]}>
            <View style={styles.playerInfoContainer}>
              {players.length > 0 ? (
                <>
                  <Text style={styles.playerName}>{players[0].name}</Text>
                  <View style={styles.scoreContainer}>
                    <Image source={coinImg} style={styles.coinIcon} />
                    <Text style={styles.scoreText}>{players[0].score}</Text>
                  </View>
                  <View style={styles.rankContainer}>
                    <Text style={styles.rankNumberText}>1</Text>
                  </View>
                </>
              ) : (
                <View style={styles.rankContainer}>
                  <Text style={styles.rankNumberText}>1</Text>
                </View>
              )}
            </View>
          </LinearGradient>
        </View>

        {/* Top 3 - Vị trí thứ 3 */}
        <View style={{...styles.top3Position, flex: 1}}>
          <View style={[styles.avatarContainer]}>
            {players.length > 2 ? (
              renderAvatar(players[2], 65)
            ) : (
              <View style={styles.emptyAvatarPlaceholder} />
            )}
          </View>
          <LinearGradient
            colors={['#4FA3A5', '#4FA3A5']}
            style={[styles.podiumColumn]}>
            <View style={styles.playerInfoContainer}>
              {players.length > 2 ? (
                <>
                  <Text style={styles.playerName}>{players[2].name}</Text>
                  <View style={styles.scoreContainer}>
                    <Image source={coinImg} style={styles.coinIcon} />
                    <Text style={styles.scoreText}>{players[2].score}</Text>
                  </View>
                  <View style={styles.rankContainer}>
                    <Text style={styles.rankNumberText}>3</Text>
                  </View>
                </>
              ) : (
                <View style={styles.rankContainer}>
                  <Text style={styles.rankNumberText}>3</Text>
                </View>
              )}
            </View>
          </LinearGradient>
        </View>
      </View>
    );
  };

  // Render item trong danh sách top 4-10
  const renderItem = ({item, index}: {item: Player; index: number}) => {
    // Chỉ hiển thị từ top 4 trở đi
    if (index < 3) {
      return null;
    }

    return (
      <View style={styles.listItemContainer}>
        <Text style={styles.rankText}>{index + 1}</Text>
        {renderAvatar(item, 50)}
        <Text style={styles.listItemName}>{item.name}</Text>
        <View style={styles.listItemScoreContainer}>
          <Image
            source={require('../ailatrieuphu/assets/coin-icon.png')}
            style={styles.listItemCoinIcon}
          />
          <Text style={styles.listItemScoreText}>{item.score}</Text>
        </View>
      </View>
    );
  };

  return (
    <LinearGradient colors={['#FF9966', '#FF5E62']} style={styles.container}>
      <SafeAreaView edges={['top']} style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <AppButton
            prefixIcon={'outline/user interface/e-remove'}
            prefixIconSize={20}
            backgroundColor={
              ColorThemes.light.Neutral_Background_Color_Absolute
            }
            textColor={ColorThemes.light.Neutral_Text_Color_Title}
            borderColor="transparent"
            containerStyle={styles.backButton}
            onPress={navigateBack}
          />
          <Text style={styles.headerTitle}>Sakupi</Text>
          <View style={styles.headerRightPlaceholder} />
        </View>

        {loading ? (
          <ActivityIndicator size="large" color="#fff" style={styles.loader} />
        ) : players.length === 0 ? (
          <EmptyPage title="Chưa có dữ liệu xếp hạng" />
        ) : (
          <>
            {/* Top 3 Players */}
            {renderTop3()}

            {/* Divider */}

            {/* List of Players (4-10) */}
            <View style={styles.listContainer}>
              {/* Tiêu đề các cột */}
              <View style={styles.listHeaderContainer}>
                <View style={styles.listHeaderRank}>
                  <Text style={styles.listHeaderText}>Hạng</Text>
                </View>
                <View style={styles.listHeaderAvatar} />
                <View style={styles.listHeaderName}>
                  <Text style={styles.listHeaderText}>Tên</Text>
                </View>
                <View style={styles.listHeaderScore}>
                  <Text style={styles.listHeaderText}>Sakupi</Text>
                </View>
              </View>

              <FlatList
                data={players}
                renderItem={renderItem}
                keyExtractor={item => item.customerId}
                contentContainerStyle={styles.listContent}
              />
            </View>
          </>
        )}
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 56,
  },
  backButton: {
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  headerTitle: {
    ...TypoSkin.heading5,
    color: '#FFFFFF',
    textAlign: 'center',
  },
  headerRightPlaceholder: {
    width: 40,
  },
  podiumContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginTop: 16,
    alignItems: 'flex-end',
  },
  podiumColumn: {
    flex: 1,
    paddingTop: 15,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  podiumColumnFirst: {
    height: Dimensions.get('window').height / 2.5, // Top 1 cao nhất
  },
  top2Container: {
    height: Dimensions.get('window').height / 3, // Top 2 cao hơn top 3 và thấp hơn top 1
  },
  top3Position: {
    height: Dimensions.get('window').height / 3.4, // Top 3 thấp nhất
  },
  avatarContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 100,
  },
  playerInfoContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-start',
    width: '100%',
    paddingTop: 10,
    marginTop: 5,
    paddingBottom: 10,
    height: '100%',
  },
  rankContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 'auto',
    position: 'absolute',
    bottom: -10,
  },
  emptyTopContainer: {
    width: 80,
    height: 120,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyAvatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  avatar: {
    borderRadius: 50,
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  avatarPlaceholder: {
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  avatarText: {
    ...TypoSkin.heading5,
    color: '#FFFFFF',
  },
  playerName: {
    ...TypoSkin.body2,
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: 'bold',
    paddingHorizontal: 10,
    position: 'absolute',
    top: '0%',
    transform: [{translateY: -10}],
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 5,
    position: 'absolute',
    top: '50%',
    transform: [{translateY: -10}],
  },
  coinIcon: {
    width: 16,
    height: 16,
    marginRight: 4,
  },
  scoreText: {
    ...TypoSkin.subtitle2,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  crownIcon: {
    width: 40,
    height: 40,
    position: 'absolute',
    top: -20,
    zIndex: 1,
  },
  rankNumberText: {
    ...TypoSkin.heading3,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: {width: 1, height: 1},
    textShadowRadius: 2,
    paddingBottom: 24,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 16,
    marginVertical: 20,
  },
  listContainer: {
    flex: 1,
    backgroundColor: '#F6F5F3',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 16,
  },
  listHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginBottom: 16,
  },
  listHeaderRank: {
    width: 50,
    alignItems: 'center',
  },
  listHeaderAvatar: {
    width: 50,
  },
  listHeaderName: {
    flex: 1,
  },
  listHeaderScore: {
    width: 80,
    alignItems: 'center',
  },
  listHeaderText: {
    ...TypoSkin.subtitle2,
    color: '#666666',
    fontWeight: 'bold',
  },
  listTitle: {
    ...TypoSkin.heading6,
    color: '#000',
    marginLeft: 16,
    marginBottom: 16,
  },
  listContent: {
    paddingHorizontal: 16,
  },
  listItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  rankText: {
    ...TypoSkin.heading6,
    color: '#333333',
    width: 30,
    textAlign: 'center',
  },
  listItemName: {
    ...TypoSkin.subtitle1,
    color: '#333333',
    flex: 1,
    marginLeft: 12,
  },
  listItemScoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(79, 163, 165, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  listItemCoinIcon: {
    width: 16,
    height: 16,
    marginRight: 4,
  },
  listItemScoreText: {
    ...TypoSkin.subtitle2,
    color: '#4FA3A5',
    fontWeight: 'bold',
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default RankingScreen;
