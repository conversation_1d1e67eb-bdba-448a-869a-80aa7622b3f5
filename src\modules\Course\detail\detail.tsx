import {
  Image,
  Text,
  useWindowDimensions,
  View,
  RefreshControl,
} from 'react-native';
import TitleWithBackAction from '../../../Screen/Layout/titleWithBackAction';
import {use, useEffect, useMemo, useRef, useState} from 'react';
import {AppButton, FDialog, FLoading, Winicon} from 'wini-mobile-components';
import {TabBar, TabView} from 'react-native-tab-view';
import {ColorThemes} from '../../../assets/skin/colors';
import Overview from './overview';
import WScreenFooter from '../../../Screen/Layout/footer';
import {TypoSkin} from '../../../assets/skin/typography';
import Lesson from './lesson';
import RatingIndex from './rating';
import Instructor from './instrutor';
import {useNavigation, useRoute} from '@react-navigation/native';
import {CourseDA} from '../da';
import {Ultis} from '../../../utils/Utils';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import {onShare} from '../../../features/share';
import {CourseActions} from '../../../redux/reducers/courseReducer';
import ConfigAPI from '../../../Config/ConfigAPI';
import {navigate, RootScreen} from '../../../router/router';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {LoadingUI} from '../learn/documentTab';
import {useTranslation} from 'react-i18next';

export default function CourseDetail() {
  const {t} = useTranslation();
  const [loading, setLoading] = useState(true);
  const [like, setLike] = useState(false);
  const [index, setIndex] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const route = useRoute<any>();
  const {id, check} = route.params;
  const [data, setData] = useState<any>();
  const [routes, setrouters] = useState<Array<any>>([]);
  const layout = useWindowDimensions();
  const dispatch: AppDispatch = useDispatch();
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const dialogRef = useRef<any>(null);
  const scrollviewRef = useRef<any>(null);
  const courseDA = new CourseDA();

  const [bought, setBought] = useState(false); // check buy

  useEffect(() => {
    getData();
    if (check) {
      setrouters([
        {key: 'overview', title: t('course.overview')},
        {key: 'rating', title: t('course.rating')},
        {key: 'instructor', title: t('course.instructor')},
      ]);
    } else {
      setrouters([
        {key: 'overview', title: t('course.overview')},
        {key: 'lesson', title: t('course.lessons')},
        {key: 'rating', title: t('course.rating')},
        {key: 'instructor', title: t('course.instructor')},
      ]);
    }
  }, []);

  const getData = async () => {
    if (!id) {
      setLoading(false);
      setRefreshing(false);
      return;
    }
    const result = await courseDA.getCourseDetail(id);
    if (result) {
      const tmp = {
        ...result.data,
        IsLike: await courseDA.checkCourseIsWishlishCustomer(result.data.Id),
      };

      setData(tmp);
      const rsBought = await courseDA.getMyCourse();
      if (rsBought) {
        const lst = rsBought?.map((item: any) => item.CourseId);
        setBought(lst.includes(id));
      }
      setLoading(false);
      setRefreshing(false);
      await dispatch(CourseActions.addRecent(tmp));
    } else {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    getData();
  };

  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      activeColor={ColorThemes.light.Primary_Color_Main}
      indicatorStyle={{
        backgroundColor: ColorThemes.light.Primary_Color_Main,
        height: 1.5,
      }}
      onTabPress={() => {}}
      tabStyle={{paddingHorizontal: 4, paddingTop: 0}}
      inactiveColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
      style={{
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        height: 45,
        elevation: 0,
      }}
    />
  );

  const visibleImg = useMemo(() => {
    return scrollviewRef?.current?.contentOffset?.y > 100;
  }, [scrollviewRef?.current?.contentOffset?.y]);

  const renderScene = ({route}: any) => {
    const refreshProps = {
      refreshControl: (
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[ColorThemes.light.Primary_Color_Main]}
          tintColor={ColorThemes.light.Primary_Color_Main}
        />
      ),
    };

    switch (route.key) {
      case 'overview':
        return (
          <Overview
            scrollviewRef={scrollviewRef}
            data={data}
            check={check}
            refreshControlProps={refreshProps}
          />
        );
      case 'lesson':
        return check ? null : (
          <Lesson
            scrollviewRef={scrollviewRef}
            data={data}
            checkBuy={bought}
            refreshControlProps={refreshProps}
          />
        );
      case 'rating':
        return (
          <RatingIndex
            scrollviewRef={scrollviewRef}
            data={data}
            checkBuy={bought}
            refreshControlProps={refreshProps}
          />
        );
      case 'instructor':
        return (
          <Instructor
            scrollviewRef={scrollviewRef}
            data={data}
            refreshControlProps={refreshProps}
          />
        );
      default:
        return null;
    }
  };
  return (
    <TitleWithBackAction
      iconAction="fill/arrows/share"
      iconActionPress={() => {
        onShare({content: 'Hello world'});
      }}>
      <FDialog ref={dialogRef} />
      {loading ? (
        <LoadingUI />
      ) : (
        <View style={{flex: 1}}>
          {check || visibleImg ? (
            <Text
              style={{
                ...TypoSkin.heading6,
                color: ColorThemes.light.Neutral_Text_Color_Title,
                paddingHorizontal: 16,
                paddingBottom: 16,
              }}>
              {data?.Name ?? ''}
            </Text>
          ) : (
            <View style={{height: 220}}>
              <Image
                onLoad={() =>
                  scrollviewRef?.current?.scrollTo({y: 0, animated: false})
                }
                key={data?.Img}
                source={{
                  uri: data?.Img
                    ? `${ConfigAPI.urlImg + data?.Img}`
                    : 'https://www.figma.com/file/QeG7fLsM5o0Oje9Wagi1xc/image/be9e79d2b2cc1e79b9b9d50cba88c6febddd5d7f',
                }}
                height={220}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  position: 'absolute',
                }}
                resizeMode="cover"
              />
            </View>
          )}
          <TabView
            navigationState={{index, routes}}
            renderScene={renderScene}
            renderTabBar={renderTabBar}
            swipeEnabled={false}
            onIndexChange={setIndex}
            initialLayout={{width: layout.width}}
          />
          {check ? (
            <></>
          ) : (
            <WScreenFooter
              style={{
                flexDirection: 'row',
                gap: 8,
                paddingHorizontal: 16,
                paddingBottom: 32,
                alignItems: 'center',
              }}>
              <Text
                style={[
                  {
                    ...TypoSkin.heading6,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                  },
                ]}>
                {t('course.total')}: {Ultis.money(data.Price)} vnđ
              </Text>
              <View style={{flex: 1}} />
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                containerStyle={{
                  borderRadius: 8,
                  paddingHorizontal: 12,
                  paddingVertical: 5,
                }}
                onPress={async () => {
                  if (!user) {
                    dialogCheckAcc(dialogRef);
                    return;
                  }
                  if (data.IsLike === true) {
                    const result = await courseDA.deleteWishlistCourse(data.Id);
                    if (result.code === 200) {
                      const d = {...data, IsLike: false};
                      setData(d);
                    }
                  } else {
                    const result = await courseDA.addWishlistCourse(data.Id);
                    if (result) {
                      const d = {...data, IsLike: true};
                      setData(d);
                    }
                  }
                }}
                textColor={
                  like
                    ? ColorThemes.light.Error_Color_Main
                    : ColorThemes.light.Neutral_Background_Color_Absolute
                }
                title={
                  <Winicon
                    src={
                      data?.IsLike
                        ? 'fill/user interface/heart'
                        : 'outline/user interface/heart'
                    }
                    size={16}
                    color={
                      data?.IsLike
                        ? ColorThemes.light.Error_Color_Main
                        : ColorThemes.light.Neutral_Text_Color_Subtitle
                    }
                  />
                }
              />
              {loading ? null : (
                <AppButton
                  title={
                    user && bought ? t('course.learn') : t('course.buyNow')
                  }
                  backgroundColor={ColorThemes.light.Primary_Color_Main}
                  borderColor="transparent"
                  containerStyle={{
                    borderRadius: 8,
                    paddingHorizontal: 12,
                    paddingVertical: 5,
                  }}
                  onPress={() => {
                    if (!user) {
                      dialogCheckAcc(dialogRef);
                      return;
                    }
                    if (bought) {
                      navigate(RootScreen.navigateESchoolView, {
                        screen: 'Learn',
                      });
                      setTimeout(() => {
                        navigation.push(RootScreen.ProccessCourse, {
                          id: data.Id,
                          name: data.Name,
                          isCertificate: data.IsCertificate,
                          author: data.Author,
                          signature: data.Signature,
                          certificateName: data.CertificateName,
                        });
                      }, 300);
                      return;
                    }
                    navigation.push(RootScreen.order, {id: id});
                  }}
                  textColor={
                    ColorThemes.light.Neutral_Background_Color_Absolute
                  }
                />
              )}
            </WScreenFooter>
          )}
        </View>
      )}
    </TitleWithBackAction>
  );
}
