import {ScrollView, Text, View, StyleSheet, Image} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {ListTile, Winicon} from 'wini-mobile-components';
import {useEffect} from 'react';
import {useTranslation} from 'react-i18next';

export default function GeneralTab({lessonData}: any) {
  const {t} = useTranslation();
  return (
    <ScrollView style={styles.container}>
      {lessonData && (
        <View style={styles.container}>
          {/* <Text style={styles.sectionTitle}>{data.Name}</Text> */}
          <View style={styles.lessonInfo}>
            <ListTile
              leading={<Winicon src="fill/technology/video-player" size={24} />}
              title={lessonData.Name || t('course.noTitle')}
              subtitle={t('course.duration', {hours: lessonData.Hours || '0'})}
              listtileStyle={styles.listTile}
              leadingContainer={styles.leadingContainer}
            />
          </View>
          {lessonData.Introduction && (
            <View style={styles.introSection}>
              <Text style={styles.introContent}>
                {lessonData.Description || t('course.noDescription')}
              </Text>
            </View>
          )}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // padding: 16,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  sectionTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    // marginBottom: 12,
  },
  lessonInfo: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    overflow: 'hidden',
  },
  courseInfo: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    overflow: 'hidden',
  },
  listTile: {
    // padding: 12,
    gap: 16, // Add gap between elements
  },
  leadingContainer: {
    // marginRight: 12, // Add extra space between icon and text
  },
  introSection: {
    paddingLeft: 16,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  introTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 8,
  },
  introContent: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
});
