import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Vibration,
  Alert,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import Sound from 'react-native-sound';
import Question from './question';
import ScreenHeader from '../../../Screen/Layout/header';
import {SafeAreaView} from 'react-native-safe-area-context';
import {AppButton} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {navigateBack} from '../../../router/router';
import {TypoSkin} from '../../../assets/skin/typography';

export interface Question {
  text: string;
  options: string[];
  correctAnswer: string;
}

const QuizScreenFlashcard: React.FC<{route: any}> = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const {lst} = route.params;
  const [questions, setQuestions] = useState<Array<Question>>([]);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(60);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [streak, setStreak] = useState(0);
  const fadeAnim = new Animated.Value(0);
  const progressAnim = new Animated.Value(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const correctSound = new Sound('correct.mp3', Sound.MAIN_BUNDLE, error => {
    if (error) console.log('Failed to load sound', error);
  });
  const wrongSound = new Sound('Incorrect.mp3', Sound.MAIN_BUNDLE, error => {
    if (error) console.log('Failed to load sound', error);
  });
  const timeoutSound = new Sound('Incorrect.mp3', Sound.MAIN_BUNDLE, error => {
    if (error) console.log('Failed to load sound', error);
  });

  const shuffleArray = (array: any[]) => {
    return array.sort(() => Math.random() - 0.5);
  };

  const getQuestions = (rawQuestions: any) => {
    const questions: Question[] = rawQuestions.map((item: any) => {
      const otherDefines = rawQuestions
        .filter((q: any) => q.Id !== item.Id)
        .map((q: any) => q.Define);
      const wrongAnswers = shuffleArray(otherDefines).slice(0, 3);
      const options = shuffleArray([item.Define, ...wrongAnswers]);
      return {
        text: `Từ "${item.Name}" có nghĩa là gì?`,
        options,
        correctAnswer: item.Define,
      };
    });
    const finalQuestions = questions.slice(0, Math.min(20, questions.length));
    setQuestions(shuffleArray(finalQuestions));
  };

  const updateProgress = () => {
    const progressPercent = ((currentQuestion + 1) / questions.length) * 100;
    Animated.timing(progressAnim, {
      toValue: progressPercent,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  useEffect(() => {
    getQuestions(lst);
  }, []);

  useEffect(() => {
    if (questions.length === 0) return;

    setTimeLeft(60);
    fadeAnim.setValue(0);
    updateProgress();

    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();

    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          timeoutSound.play();
          Vibration.vibrate(500);
          handleNextQuestion();
          return 60;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [currentQuestion, questions]);

  const handleAnswer = (answer: string) => {
    if (selectedAnswer) return;
    setSelectedAnswer(answer);
    const isAnswerCorrect = answer === questions[currentQuestion].correctAnswer;
    setIsCorrect(isAnswerCorrect);
    if (isAnswerCorrect) {
      setScore(score + 1);
      setStreak(streak + 1);
      correctSound.play();
    } else {
      setStreak(0);
      wrongSound.play();
      Vibration.vibrate(200);
    }
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setTimeout(handleNextQuestion, 1500);
  };

  const handleNextQuestion = () => {
    if (currentQuestion + 1 < questions.length) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer(null);
      setIsCorrect(null);
    } else {
      endQuiz();
    }
  };

  const endQuiz = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    navigation.navigate('Home');
    Alert.alert(`Quiz Ended! Your Score: ${score}/${questions.length}`);
  };

  if (questions.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.score}>Loading...</Text>
      </View>
    );
  }

  return (
    <LinearGradient
      colors={['#FFE5B4', '#FFF8E1']} // Gradient từ cam nhạt đến trắng
      style={styles.container}>
      <SafeAreaView edges={['top']} />
      <View style={{padding: 16, flex: 1}}>
        <AppButton
          prefixIcon={'outline/user interface/e-remove'}
          prefixIconSize={20}
          backgroundColor={ColorThemes.light.Neutral_Background_Color_Absolute}
          textColor={ColorThemes.light.Neutral_Text_Color_Title}
          borderColor="transparent"
          containerStyle={{
            position: 'absolute',
            top: 14,
            left: 16,
            zIndex: 111,
            paddingHorizontal: 12,
            borderRadius: 100,
            width: 40,
            height: 40,
          }}
          onPress={navigateBack}
        />
        <Text style={styles.title}>Kiểm tra</Text>
        <View style={styles.header}>
          <Text style={styles.score}>
            Score: {score} | Streak: {streak}
          </Text>
          <Text style={styles.progressText}>
            {currentQuestion + 1}/{questions.length}
          </Text>
        </View>
        <View style={styles.progressContainer}>
          <LinearGradient
            colors={['#FFCA28', '#FF9800']} // Gradient cho thanh tiến trình
            style={styles.progressBarContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 100],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </LinearGradient>
        </View>
        <View style={{flexDirection: 'row', justifyContent: 'flex-end'}}>
          <Text style={styles.timer}>Time Left: {timeLeft}s</Text>
        </View>
        <Animatable.View
          animation="zoomIn"
          duration={500}
          style={styles.questionContainer}>
          <Question
            question={questions[currentQuestion]}
            onAnswer={handleAnswer}
            selectedAnswer={selectedAnswer}
            isCorrect={isCorrect}
          />
        </Animatable.View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    textAlign: 'center',
    marginTop: 4,
    marginBottom: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: {width: 1, height: 1},
    textShadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  score: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
  },
  progressText: {
    fontSize: 14,
    color: '#000',
  },
  progressContainer: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  progressBarContainer: {
    flex: 1,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: 'transparent',
  },
  timer: {
    fontSize: 14,
    color: '#D32F2F', // Đỏ đậm hơn
    textAlign: 'center',
    marginBottom: 16,
    fontWeight: 'bold',
  },
  instructionBox: {
    backgroundColor: '#FF5722', // Cam rực rỡ
    padding: 8,
    borderRadius: 10,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  instructionText: {
    fontSize: 16,
    color: '#FFF',
    fontWeight: 'bold',
  },
  questionContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  endButton: {
    borderRadius: 10,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  endButtonGradient: {
    padding: 15,
    alignItems: 'center',
  },
  endButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default QuizScreenFlashcard;
