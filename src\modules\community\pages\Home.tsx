/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Dimensions,
  RefreshControl,
} from 'react-native';
import {
  AppButton,
  ComponentStatus,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {DefaultPost, SkeletonPlacePostCard} from '../card/defaultPost';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {TypoSkin} from '../../../assets/skin/typography';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {useDispatch, useSelector} from 'react-redux';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import {onShare} from '../../../features/share';
import {Ultis} from '../../../utils/Utils';
import {ProfileView} from './Chat';
import {useTranslation} from 'react-i18next';
import {LogoImg} from '../../../Screen/Page/Home';

const MemoizedWinicon = React.memo(Winicon);

const getTabs = (t: any) => [
  {Id: 0, Name: t('community.tabs.popular'), Icon: 'outline/arrows/trend-up'},
  {Id: 1, Name: t('community.tabs.forYou'), Icon: 'outline/files/news'},
  {
    Id: 2,
    Name: t('community.tabs.following'),
    Icon: 'outline/editing/list-favs-2',
  },
  {
    Id: 3,
    Name: t('community.tabs.bookmark'),
    Icon: 'outline/user interface/bookmark',
  },
];

const TabItem = React.memo(({tab, index, isActive, onPress}: any) => (
  <TouchableOpacity
    key={index}
    onPress={onPress}
    style={[styles.tab, isActive && styles.activeTab]}>
    <MemoizedWinicon key={tab.Icon} src={tab.Icon} size={12} />
    <Text style={[styles.tabText]}>{tab.Name}</Text>
  </TouchableOpacity>
));

export default function Home() {
  const {t} = useTranslation();
  const user = useSelectorCustomerState().data;
  const [activeTab, setActiveTab] = React.useState(0);
  const bottomSheetRef = useRef<any>(null);
  const tabs = getTabs(t);

  const dialogRef = React.useRef<any>(null);
  const flatListRef = useRef<any>(null);
  const dispatch: AppDispatch = useDispatch();
  const navigation = useNavigation<any>();
  const size = 50;
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true); // Thêm state để kiểm tra còn data không
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const data = useSelector((state: RootState) => state.newsFeed.data);
  const {loading} = useSelector((state: RootState) => state.newsFeed);

  useEffect(() => {
    refreshData();
  }, [activeTab]);

  const handleRefresh = async () => {
    if (!loading) {
      // Thêm check này để tránh gọi refresh khi đang loading
      setIsRefreshing(true);
      setHasMore(true);
      try {
        refreshData();
      } catch (error) {
        console.error('Refresh error:', error);
      } finally {
      }
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  };

  const handleLoadMore = async () => {
    // Clear any existing timeout to prevent multiple calls
  };

  const refreshData = () => {
    if (activeTab === 0) {
      dispatch(newsFeedActions.getNewFeedPopular(1, size));
    } else if (activeTab === 1) {
      dispatch(newsFeedActions.getNewFeed(1, size));
    } else if (activeTab === 2) {
      dispatch(newsFeedActions.getNewFeedFollowing(1, size));
    } else if (activeTab === 3) {
      dispatch(newsFeedActions.getNewFeedSaved(1, size));
    }
  };

  const TabBar = React.memo(() => {
    return (
      <View>
        <ScrollView
          showsHorizontalScrollIndicator={false}
          horizontal={true}
          contentContainerStyle={{
            gap: 8,
          }}
          style={styles.tabBar}>
          {/* write 3 tab in here */}
          {tabs.map((tab, index) => (
            <TabItem
              key={`tab-${tab.Id}`}
              tab={tab}
              index={tab.Id}
              isActive={activeTab === tab.Id}
              onPress={() => {
                setActiveTab(tab.Id);
                flatListRef.current?.scrollToOffset({
                  animated: false,
                  offset: 0,
                });
              }}
            />
          ))}
        </ScrollView>
      </View>
    );
  });

  const PostItem = ({
    item,
    user,
    dialogRef,
  }: {
    item: any;
    user: any;
    dialogRef: any;
  }) => {
    return (
      <DefaultPost
        data={{
          ...item,
          relativeUser:
            item.CustomerId === user?.Id
              ? {
                  image: user?.AvatarUrl,
                  title: user?.Name,
                  subtitle: Ultis.getDiffrentTime(item.DateCreated),
                }
              : item.relativeUser,
        }}
        containerStyle={{paddingHorizontal: 0}}
        onPressDetail={() => navigate(RootScreen.PostDetail, {item: item})}
        actionView={
          <View
            style={{
              flexDirection: 'row',
              paddingTop: 16,
              alignItems: 'center',
              gap: 8,
            }}>
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              onPress={async () => {
                if (user) {
                  await dispatch(
                    newsFeedActions.updateLike(item.Id, item.IsLike === true),
                  );
                } else {
                  ///TODO: check chưa login thì confirm ra trang login
                  dialogCheckAcc(dialogRef);
                }
              }}
              title={
                <Text
                  style={{
                    ...TypoSkin.buttonText5,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  }}>
                  {item.Likes ?? 0}
                </Text>
              }
              textColor={
                item.IsLike === true
                  ? ColorThemes.light.Error_Color_Main
                  : ColorThemes.light.Neutral_Text_Color_Subtitle
              }
              prefixIconSize={12}
              prefixIcon={
                item.IsLike === true
                  ? 'fill/emoticons/heart'
                  : 'outline/emoticons/heart'
              }
            />
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              onPress={async () => {
                navigate(RootScreen.PostDetail, {item: item});
              }}
              prefixIcon={'outline/user interface/b-comment'}
              prefixIconSize={12}
              textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
              title={
                <Text
                  style={{
                    ...TypoSkin.buttonText5,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  }}>
                  {item.Comment ?? 0}
                </Text>
              }
            />
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              onPress={async () => {
                onShare({content: 'Hello world'});
              }}
              prefixIcon={'fill/arrows/social-sharing'}
              prefixIconSize={12}
              textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
            />
          </View>
        }
        onPressHeader={() => {
          if (item.GroupId) {
            navigate(RootScreen.GroupIndex, {Id: item.GroupId});
          } else {
            navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
          }
        }}
        trailingView={
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 4,
            }}>
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              onPress={async () => {
                if (user) {
                  await dispatch(
                    newsFeedActions.addBookmark(
                      item.Id,
                      item.IsBookmark === true,
                    ),
                  );
                } else {
                  ///TODO: check chưa login thì confirm ra trang login
                  dialogCheckAcc(dialogRef);
                }
              }}
              containerStyle={{
                borderRadius: 100,
                padding: 6,
                height: 24,
                width: 24,
              }}
              title={
                <Winicon
                  src={
                    item.IsBookmark === true
                      ? 'fill/user interface/bookmark'
                      : 'outline/user interface/bookmark'
                  }
                  size={14}
                  color={
                    item.IsBookmark === true
                      ? ColorThemes.light.Warning_Color_Main
                      : ColorThemes.light.Neutral_Text_Color_Subtitle
                  }
                />
              }
            />
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  title: t('community.actions'),
                  suffixAction: <View />,
                  prefixAction: (
                    <TouchableOpacity
                      onPress={() => hideBottomSheet(bottomSheetRef)}
                      style={{padding: 6, alignItems: 'center'}}>
                      <Winicon
                        src="outline/layout/xmark"
                        size={20}
                        color={ColorThemes.light.Neutral_Text_Color_Body}
                      />
                    </TouchableOpacity>
                  ),
                  children: (
                    <View
                      style={{
                        gap: 8,
                        height: Dimensions.get('window').height / 4,
                        width: '100%',
                        backgroundColor:
                          ColorThemes.light.Neutral_Background_Color_Absolute,
                      }}>
                      <ListTile
                        onPress={() => {
                          hideBottomSheet(bottomSheetRef);

                          showSnackbar({
                            message: t('community.featureInDevelopment'),
                            status: ComponentStatus.WARNING,
                          });
                        }}
                        title={t('community.reportPost')}
                        titleStyle={{...TypoSkin.body3}}
                      />
                      {item.CustomerId === user.Id && (
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);

                            navigation.push(RootScreen.createPost, {
                              editPost: item,
                              groupId: null,
                            });
                            // showSnackbar({
                            //   message: 'Chức năng đang được phát triển',
                            //   status: ComponentStatus.WARNING,
                            // });
                          }}
                          title={t('community.editPost')}
                          titleStyle={{...TypoSkin.body3}}
                        />
                      )}
                      {item.CustomerId === user.Id && (
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);
                            dispatch(newsFeedActions.deletePost(item));
                          }}
                          title={t('community.deletePost')}
                          titleStyle={{...TypoSkin.body3}}
                        />
                      )}
                    </View>
                  ),
                });
              }}
              containerStyle={{
                borderRadius: 100,
                padding: 6,
                height: 24,
                width: 24,
              }}
              title={
                <Winicon
                  src={'fill/user interface/menu-dots'}
                  size={14}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                />
              }
            />
          </View>
        }
        showContent={true}
      />
    );
  };
  const getItemLayout = React.useCallback(
    (data: any, index: number) => ({
      length: 300, // Approximate height of each item
      offset: 300 * index,
      index,
    }),
    [],
  );

  return (
    <View style={styles.container}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={bottomSheetRef} />

      <StatusBar
        barStyle="dark-content"
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Absolute}
      />
      {/* header */}
      <ListTile
        style={{
          padding: 0,
          paddingBottom: 8,
          paddingHorizontal: 16,
        }}
        isClickLeading
        leading={
          <TouchableOpacity
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
            style={{padding: 4}}>
            <LogoImg />
          </TouchableOpacity>
        }
        title={t('community.tabs.home')}
        trailing={
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <TouchableOpacity
              style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: '#f0f0f0',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 8,
              }}
              onPress={() => {
                if (!user) {
                  dialogCheckAcc(dialogRef);
                  return;
                }
                navigation.push(RootScreen.createPost, {groupId: null});
              }}>
              <Winicon
                src="outline/layout/plus"
                size={20}
                color={ColorThemes.light.Neutral_Text_Color_Title}
              />
            </TouchableOpacity>
            <ProfileView />
          </View>
        }
      />
      {/*  */}
      <TabBar />

      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={({item}) => (
          <PostItem item={item} user={user} dialogRef={dialogRef} />
        )}
        keyExtractor={item => item?.Id.toString()}
        getItemLayout={getItemLayout}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        windowSize={10}
        initialNumToRender={5}
        showsVerticalScrollIndicator={false}
        style={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        contentContainerStyle={{
          gap: data.length == 0 ? 0 : 8,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
        }}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={() => {
          if (loading && data.length === 0) {
            return (
              <View
                style={{
                  gap: 8,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                }}>
                {[1, 2, 3].map((_, index) => (
                  <SkeletonPlacePostCard key={`skeleton-${index}`} />
                ))}
              </View>
            );
          } else {
            return (
              <View
                style={{
                  flex: 1,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                }}>
                <EmptyPage title="Không có dữ liệu" />
              </View>
            );
          }
        }}
        ListFooterComponent={() => {
          if (loading && !isRefreshing) {
            return (
              <View
                style={{
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                }}>
                <SkeletonPlacePostCard />
              </View>
            );
          }
          if (!hasMore && data?.length > 0) {
            return <EmptyPage title="Không còn dữ liệu" />;
          }
          return null;
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 0,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  tabBar: {
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
    borderWidth: 1,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderWidth: 0,
  },
  tabText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginLeft: 4,
  },
  activeTabText: {
    color: '#333',
    fontWeight: 'bold',
  },
  postContainer: {
    backgroundColor: '#fff',
    marginBottom: 8,
  },
  defaultPostContainer: {
    paddingHorizontal: 0,
  },
  postHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookmarkButton: {
    marginRight: 16,
  },
  postFooter: {
    flexDirection: 'row',
    paddingTop: 16,
    alignItems: 'center',
    gap: 8,
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  footerButtonText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
});
