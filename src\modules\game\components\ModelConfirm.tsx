import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
} from 'react-native';

const {width} = Dimensions.get('window');

const ModelConfirm = ({
  isShow,
  closeModal,
  onConfirm,
}: {
  isShow: boolean;
  closeModal: () => void;
  onConfirm: () => void;
}) => {
  const handleConfirm = () => {
    closeModal();
    onConfirm();
  };
  return (
    <View style={styles.container}>
      {/* Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={isShow}
        onRequestClose={closeModal}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            {/* Icon cảnh báo */}
            <View style={styles.warningIcon}>
              <Text style={styles.warningIconText}>!</Text>
            </View>

            {/* Nội dung thông báo */}
            <Text style={styles.modalText}>
              Bạn sẽ bị trừ 10 điểm khi sử dụng trợ giúp này
            </Text>

            {/* Container cho các nút */}
            <View style={styles.buttonContainer}>
              {/* Nút Hủy */}
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={closeModal}
                activeOpacity={0.8}>
                <Text style={styles.cancelButtonText}>Hủy</Text>
              </TouchableOpacity>

              {/* Nút Đồng ý */}
              <TouchableOpacity
                style={[styles.button, styles.agreeButton]}
                onPress={handleConfirm}
                activeOpacity={0.8}>
                <Text style={styles.agreeButtonText}>Đồng ý</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  showButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  showButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    width: width * 0.8,
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  warningIcon: {
    width: 50,
    height: 50,
    backgroundColor: '#FF8C00',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  warningIconText: {
    color: 'white',
    fontSize: 28,
    fontWeight: 'bold',
  },
  modalText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#007AFF',
  },
  agreeButton: {
    backgroundColor: '#DC3545',
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  agreeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ModelConfirm;
