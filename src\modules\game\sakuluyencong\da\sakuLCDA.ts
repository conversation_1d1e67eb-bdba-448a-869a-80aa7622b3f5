import { DataController } from '../../../../base/baseController';
import {
  SakuLCGameConfigAPI,
  SakuLCGameQuestionAPI,
  SakuLCGameAnswerAPI,
  SakuLCGameConfig,
  SakuLCQuestion,
  SakuLCWord,
  ApiResponse,
} from '../types/sakuLCTypes';

export class SakuLCDA {
  private questionController: DataController;
  private answerController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
  }

  /**
   * <PERSON><PERSON><PERSON> cấu hình game từ bảng GameConfig
   * @param gameId ID của game SakuLC
   * @returns Promise<SakuLCGameConfig>
   */
  static async getGameConfig(gameId: string): Promise<SakuLCGameConfig> {
    try {
      console.log(`[SakuLCDA] Loading game config for GameId: ${gameId}`);

      const controller = new DataController('GameConfig');
      const response: ApiResponse<SakuLCGameConfigAPI> = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });
      if (response.code !== 200 || !response.data || response.data.length === 0) {
        throw new Error('No game config found or API returned unsuccessful response');
      }

      const configData = response.data[0];
      const transformedConfig: SakuLCGameConfig = {
        gameId: gameId,
        scorePerLife: configData.Score || 10,
        maxLives: configData.LifeCount || 3,
        timeLimit: configData.Time || 300,
        bonusScore: configData.Bonus || 50,
        isActive: configData.IsActive || true,
      };

      console.log('[SakuLCDA] Successfully loaded game config:', transformedConfig);
      return transformedConfig;

    } catch (error) {
      console.error('[SakuLCDA] Error loading game config:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách câu hỏi từ bảng GameQuestion
   * @param gameId ID của game
   * @param stage Stage của game
   * @param competenceId ID competence
   * @returns Promise<SakuLCGameQuestionAPI[]>
   */
  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number,
    competenceId: string
  ): Promise<SakuLCGameQuestionAPI[]> {
    try {
      console.log(`[SakuLCDA] Loading questions for GameId: ${gameId}, Stage: ${stage}, CompetenceId: ${competenceId}`);

      const response: ApiResponse<SakuLCGameQuestionAPI> = await this.questionController.getListSimple({
        query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
        sortby: { BY: 'Sort', DIRECTION: 'ASC' },
      });
      if (response.code !== 200) {
        throw new Error(`API returned error code: ${response.code}`);
      }
      //lấy danh sách answer
      const answerResponse = await this.answerController.getListSimple({
        query: `@GameQuestionId: {${response.data.map((q: any) => q.Id).join(' | ')}}`,
      });
      if (answerResponse && answerResponse.data && answerResponse.data.length > 0) {
        // Map các câu trả lời vào câu hỏi
        response.data.forEach((question: any) => {
          question.Answers = answerResponse.data.filter((answer: any) => answer.GameQuestionId === question.Id);
        });
      }

      const questions = response.data || [];
      return questions;

    } catch (error) {
      throw error;
    }
  }

  /**
   * Transform raw API data thành format phù hợp cho game
   * @param rawQuestions Dữ liệu thô từ API (đã có Answers property)
   * @returns SakuLCQuestion[]
   */
  static transformQuestionsWithAnswers(
    rawQuestions: SakuLCGameQuestionAPI[]
  ): SakuLCQuestion[] {
    return rawQuestions.map(question => {
      // Lấy answers từ question.Answers (đã được load trong getQuestionsByGameAndStage)
      const questionAnswers = (question as any).Answers || [];

      // Transform answers thành words
      const words: SakuLCWord[] = questionAnswers.map((answer: SakuLCGameAnswerAPI) => ({
        id: answer.Id,
        text: answer.Name,
        correctPosition: answer.Sort,
      }));
      return {
        id: question.Id,
        questionText: question.Name,
        audioUrl: question.Audio,
        words: words,
        stage: question.Stage,
        competenceId: question.Purpose,
      };
    });
  }

  /**
   * Kiểm tra đáp án của người chơi
   * @param userAnswer Mảng words theo thứ tự người chơi sắp xếp
   * @param correctAnswer Mảng words với thứ tự đúng
   * @returns boolean
   */
  static checkAnswer(userAnswer: SakuLCWord[], correctAnswer: SakuLCWord[]): boolean {
    if (userAnswer.length !== correctAnswer.length) {
      return false;
    }

    // Sort user answer theo position hiện tại
    const sortedUserAnswer = [...userAnswer].sort((a, b) => (a.currentPosition || 0) - (b.currentPosition || 0));

    // Sort correct answer theo correctPosition
    const sortedCorrectAnswer = [...correctAnswer].sort((a, b) => a.correctPosition - b.correctPosition);

    // So sánh từng từ
    for (let i = 0; i < sortedUserAnswer.length; i++) {
      if (sortedUserAnswer[i].text !== sortedCorrectAnswer[i].text) {
        return false;
      }
    }

    return true;
  }
}
