import {createSlice, PayloadAction} from '@reduxjs/toolkit';

interface GameState {
  gem: number;
  cup: number;
  totalLives: number;
  currentLives: number;
  isGameOver: boolean;
  time: number;
  messageGameOver: string;
  isRunTime: boolean;
}

interface GameStateItem {
  stateName: keyof GameState;
  value: GameState[keyof GameState];
}

const initialState: GameState = {
  gem: 300,
  cup: 100,
  totalLives: 3,
  currentLives: 3,
  isGameOver: false,
  time: 500,
  messageGameOver: 'Rất tiếc!',
  isRunTime: false,
};

export const GameSlice = createSlice({
  name: 'Game',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    restartGame: (state: GameState) => {
      state.currentLives = initialState.currentLives;
      state.isGameOver = false;
      state.time = 500;
      state.isRunTime = true;
    },
    gameOver: (state: GameState, action: PayloadAction<string>) => {
      state.messageGameOver = action.payload;
      state.isGameOver = true;
      state.isRunTime = false;
    },
    reset: (state: GameState) => {
      state.currentLives = initialState.currentLives;
      state.isGameOver = false;
      state.time = 500;
      state.isRunTime = true;
    },
  },
});

export const {setData, reset, restartGame, gameOver} = GameSlice.actions;

export default GameSlice.reducer;
