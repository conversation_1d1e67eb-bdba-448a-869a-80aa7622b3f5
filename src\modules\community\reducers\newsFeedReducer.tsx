import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {DataController} from '../../../base/baseController';
import {randomGID, Ultis} from '../../../utils/Utils';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {StorageContanst} from '../../../Config/Contanst';
import ConfigAPI from '../../../Config/ConfigAPI';
import store from '../../../redux/store/store';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {GroupDA} from '../groups/da';
export const FETCH_NEWS_REQUEST = 'FETCH_NEWS_REQUEST';
export const FETCH_NEWS_REQUEST_LOADMORE = 'FETCH_NEWS_REQUEST_LOADMORE';
export const UPDATE_LIKE = 'UPDATE_LIKE';
export const UPDATE_LIKE_COMMENT = 'UPDATE_LIKE_COMMENT';
export const ADD_COMMENT = 'ADD_COMMENT';
export const ADD_BOOKMARK = 'ADD_BOOKMARK';
export const UPDATE_COMMENT_COUNT = 'UPDATE_COMMENT_COUNT';
export const ADD_POST = 'ADD_POST';
export const HIDE_POST = 'HIDE_POST';
export const UPDATE_POST = 'UPDATE_POST';
export const FETCH_ERROR = 'FETCH_ERROR';
const initialState: {
  data: any[];
  loading: boolean;
  loadmore: boolean;
  page: number;
  success: boolean;
  error: any; // Hoặc để null|string nếu muốn cụ thể hơn
} = {
  data: [],
  page: 1,
  loading: true,
  loadmore: false,
  success: false,
  error: null,
};
export const newsFeeedSlice = createSlice({
  name: 'newsFeed',
  initialState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case ADD_POST: {
          // Ensure new post is added at the beginning of the array
          state.data = [action.payload.post, ...state.data];
          break;
        }
        case UPDATE_POST: {
          // Cập nhật bài đăng trong state
          state.data = state.data.map(post =>
            post.Id === action.payload.post.Id
              ? {
                  ...post,
                  Content: action.payload.post.Content,
                  Img: action.payload.post.Img,
                  DateModified: action.payload.post.DateModified,
                }
              : post,
          );
          break;
        }
        case FETCH_NEWS_REQUEST:
          state.data = action.payload.data;
          state.page = action.payload.page;
          break;
        case FETCH_NEWS_REQUEST_LOADMORE:
          // Tạo một Set các ID hiện có để kiểm tra trùng lặp
          const existingIds = new Set(state.data.map((item: any) => item.Id));
          // Chỉ thêm các mục không trùng lặp
          const newItems = action.payload.data.filter(
            (item: any) => !existingIds.has(item.Id),
          );
          state.data = [...state.data, ...newItems];
          state.page = action.payload.page;
          break;
        case UPDATE_LIKE:
          state.data = state.data.map((news: any) => {
            if (news.Id === action.payload.Id) {
              return {
                ...news,
                Likes: action.payload.IsLike
                  ? (news.Likes ?? 0) + 1
                  : (news.Likes ?? 0) - 1,
                IsLike: action.payload.IsLike,
              };
            }
            return news;
          });
          break;
        case ADD_BOOKMARK:
          state.data = state.data.map((news: any) => {
            if (news.Id === action.payload.Id) {
              return {
                ...news,
                IsBookmark: action.payload.IsBookmark,
              };
            }
            return news;
          });
          break;
        case UPDATE_COMMENT_COUNT: {
          const {postId, increment} = action.payload;
          return {
            ...state,
            data: state.data.map(post =>
              post.Id === postId
                ? {...post, Comment: (post.Comment || 0) + increment}
                : post,
            ),
          };
        }
        case HIDE_POST: {
          // Xử lý ẩn bài đăng: cập nhật IsHidden và lọc khỏi danh sách hiển thị
          const postId = action.payload.postId;
          state.data = state.data.filter(post => post.Id !== postId);
          break;
        }
        case FETCH_ERROR: {
          // Handle error in a serializable way
          state.error = action.payload.error
            ? 'Error fetching news feed'
            : null;
          state.loading = false;
          break;
        }
        // Remove comment-related cases
      }
      state.loading = false;
      state.loadmore = false;
    },
    onFetching: state => {
      state.data = [];
      state.loading = true;
    },
    onLoadmore: state => {
      state.loadmore = true;
    },
  },
});
export default newsFeeedSlice.reducer;
const {handleActions, onFetching} = newsFeeedSlice.actions;
export class newsFeedActions {
  static getNewFeed =
    (page: number, size: number, search?: string) =>
    async (dispatch: Dispatch) => {
      try {
        dispatch(onFetching());
        const cusId = store.getState().customer.data?.Id;

        // Get posts based on authentication status

        const posts = await getPostsForUser(cusId, page, size ?? 10, search);
        if (!posts || posts.code !== 200) {
          return;
        }
        // Get all required data in parallel
        const enrichedPosts = await enrichPostsWithData(posts.data, cusId);
        dispatch(
          handleActions({
            type: page > 1 ? FETCH_NEWS_REQUEST_LOADMORE : FETCH_NEWS_REQUEST,
            data: enrichedPosts,
            page: page,
          }),
        );
      } catch (error) {
        console.error('Error fetching news feed:', error);
        dispatch(handleActions({type: FETCH_ERROR, error: true}));
      }
    };

  static getNewFeedFollowing =
    (page: number, size: number, search?: string) =>
    async (dispatch: Dispatch) => {
      try {
        dispatch(onFetching());
        const cusId = store.getState().customer.data?.Id;

        // Get posts based on authentication status
        const posts = await getPostsForUserFollowing(
          cusId,
          page,
          size ?? 10,
          search,
        );
        if (!posts || posts.code !== 200) {
          return;
        }
        // Get all required data in parallel
        const enrichedPosts = await enrichPostsWithData(posts.data, cusId);

        dispatch(
          handleActions({
            type: page > 1 ? FETCH_NEWS_REQUEST_LOADMORE : FETCH_NEWS_REQUEST,
            data: enrichedPosts,
            page: page,
          }),
        );
      } catch (error) {
        console.error('Error fetching news feed:', error);
        dispatch(handleActions({type: FETCH_ERROR, error: true}));
      }
    };

  // tạo action lấy bài viết đã lưu
  static getNewFeedSaved =
    (page: number, size: number) => async (dispatch: Dispatch) => {
      try {
        dispatch(onFetching());
        const cusId = store.getState().customer.data?.Id;
        const postController = new DataController('Post_Bookmark');
        const postResult = await postController.getListSimple({
          query: `@CustomerId: {*${cusId}*}`,
          page,
          size,
        });
        const postIds = postResult.data.map((post: any) => post.PostsId);
        const Postcontroller = new DataController('Posts');
        const postDetailResult = await Postcontroller.getListSimple({
          query: `@Id:{${postIds.join(' | ')}}`,
          page,
          size,
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });
        const enrichedPosts = await enrichPostsWithData(
          postDetailResult.data,
          cusId,
        );
        dispatch(
          handleActions({
            type: page > 1 ? FETCH_NEWS_REQUEST_LOADMORE : FETCH_NEWS_REQUEST,
            data: enrichedPosts,
            page: page,
          }),
        );
      } catch (error) {
        console.error('Error fetching news feed:', error);
        dispatch(handleActions({type: FETCH_ERROR, error: true}));
      }
    };

  //tạo action lấy bài viết phổ biến có nhiều lượt tương tác như like, share, bình luận
  static getNewFeedPopular =
    (page: number, size: number) => async (dispatch: Dispatch) => {
      try {
        dispatch(onFetching());
        const cusId = store.getState().customer.data?.Id;
        //lấy bài viết gần nhất trong 7 ngày
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        const startTimestamp = sevenDaysAgo.getTime();
        const endTimestamp = Date.now();
        const Postcontroller = new DataController('Posts');
        const postResult = await Postcontroller.getListSimple({
          query: `@DateCreated:[${startTimestamp} ${endTimestamp}] -@IsHidden:{true}`,
          page,
          size,
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });
        const querySearch = `@PostsId:{${postResult.data
          .map((ev: any) => ev.Id)
          .join(' | ')}}`;
        const likeResult = await fetchLikes(
          postResult.data.map((ev: any) => ev.Id),
        );
        const commentController = new DataController('Comments');
        const commentResult = await commentController.group({
          searchRaw: querySearch,
          reducers: 'GROUPBY 1 @PostsId REDUCE COUNT 0 AS totalComment',
        });
        const commentCounts = commentResult.data.reduce(
          (acc: any, res: any) => {
            acc[res.PostsId] = parseInt(res.totalComment) || 0;
            return acc;
          },
          {},
        );

        //   const posts = postResult.data.map((post: any) => {
        //       return
        //     {
        //       ...post,
        //       Likes: likeResult.filter((like: any) => like.PostsId === post.Id).length ?? 0,
        //       IsLike: cusId ? likeResult.some((like: any) => like.CustomerId === cusId) : false,
        //       Comment: commentCounts[post.Id] || 0,
        //       totalInteraction: (likeResult.filter((like: any) => like.PostsId === post.Id).length ?? 0) + (commentCounts[post.Id] || 0)
        //       // Thêm các thuộc tính khác của bài viết nếu cần
        //       // ...
        //   }}
        // );

        const posts = postResult.data.map((post: any) => {
          const likeCount =
            likeResult.filter((like: any) => like.PostsId === post.Id).length ??
            0;
          const like = likeResult.find((like: any) => like.PostsId === post.Id);
          return {
            ...post,
            Likes: likeCount,
            IsLike: like ? like.CustomerId === cusId : false,
            Comment: commentCounts[post.Id] || 0,
            totalInteraction: likeCount + (commentCounts[post.Id] ?? 0),
            // Thêm các thuộc tính khác của bài viết nếu cần
            // ...
          };
        });

        const enrichedPosts = await enrichPostsWithDataPopular(posts);
        enrichedPosts.sort(
          (a: any, b: any) => b.totalInteraction - a.totalInteraction,
        );
        dispatch(
          handleActions({
            type: page > 1 ? FETCH_NEWS_REQUEST_LOADMORE : FETCH_NEWS_REQUEST,
            data: enrichedPosts,
            page: page,
          }),
        );
      } catch (error) {
        console.error('Error fetching news feed:', error);
        dispatch(handleActions({type: FETCH_ERROR, error: true}));
      }
    };

  static addBookmark =
    (id: string, isBookmark: boolean) => async (dispatch: Dispatch) => {
      var cusId = store.getState().customer.data?.Id;
      const controller = new DataController('Post_Bookmark');

      if (!cusId) {
        return;
      }
      if (isBookmark === true) {
        const result = await controller.getListSimple({
          query: `@CustomerId: {${cusId}} @PostsId:{${id}}`,
        });
        if (result.data?.length > 0) {
          const unbookmark = await controller.delete([result.data[0].Id]);
          if (unbookmark.code === 200) {
            dispatch(
              handleActions({
                type: ADD_BOOKMARK,
                Id: id,
                IsBookmark: false,
              }),
            );
          }
        }
      } else {
        const data = {
          Id: randomGID(),
          CustomerId: cusId,
          PostsId: id,
          DateCreated: new Date().getTime(),
        };
        const result = await controller.add([data]);
        if (result.code === 200) {
          dispatch(
            handleActions({
              type: ADD_BOOKMARK,
              Id: id,
              IsBookmark: true,
            }),
          );
        }
      }
    };

  static updateLike =
    (id: string, isUnLike: boolean) => async (dispatch: Dispatch) => {
      const likeController = new DataController('Likes');
      var cusId = store.getState().customer.data?.Id;
      if (cusId) {
        if (isUnLike === true) {
          const result = await likeController.getListSimple({
            query: `@CustomerId: {${cusId}} @PostsId:{${id}}`,
          });

          if (result.data?.length > 0) {
            const unlike = await likeController.delete([result.data[0].Id]);
            if (unlike.code === 200) {
              dispatch(
                handleActions({
                  type: UPDATE_LIKE,
                  Id: id,
                  IsLike: false,
                }),
              );
            }
          }
        } else {
          const data = {
            Id: randomGID(),
            CustomerId: cusId,
            PostsId: id,
            Type: 1,
            DateCreated: new Date().getTime(),
          };
          const result = await likeController.add([data]);
          if (result.code === 200) {
            dispatch(
              handleActions({
                type: UPDATE_LIKE,
                Id: id,
                IsLike: true,
              }),
            );
          }
        }
      }
    };

  static setLike =
    (id: string, isUnLike: boolean) => async (dispatch: Dispatch) => {
      dispatch(
        handleActions({
          type: UPDATE_LIKE,
          Id: id,
          IsLike: isUnLike,
        }),
      );
    };

  static setBookmark =
    (id: string, IsBookmark: boolean) => async (dispatch: Dispatch) => {
      dispatch(
        handleActions({
          type: ADD_BOOKMARK,
          Id: id,
          IsBookmark: IsBookmark,
        }),
      );
    };

  static updateCommentCount =
    (id: string, increment: number) => async (dispatch: Dispatch) => {
      dispatch(
        handleActions({
          type: UPDATE_COMMENT_COUNT,
          postId: id,
          increment: increment,
        }),
      );
    };

  static addPost = (postData: any) => async (dispatch: Dispatch) => {
    try {
      const customer = store.getState().customer.data;

      if (!customer) {
        return null;
      }
      const postController = new DataController('Posts');
      const response = await postController.add([postData]);
      if (response?.code === 200) {
        // Get user info for the post
        if (customer) {
          const postWithUser = {
            ...postData,
            Likes: 0,
            IsLike: false,
            Comment: 0,
            IsBookmark: false,
            relativeUser: {
              image: customer.AvatarUrl,
              title: customer.Name,
              subtitle: 'Just now',
            },
          };
          // Dispatch action to add post to redux store
          dispatch(
            handleActions({
              type: ADD_POST,
              post: postWithUser,
            }),
          );

          return postWithUser;
        }
      }
      return null;
    } catch (error) {
      console.error('Error adding post:', error);
      return null;
    }
  };

  static addPostNoCall = (postData: any) => async (dispatch: Dispatch) => {
    dispatch(
      handleActions({
        type: ADD_POST,
        post: postData,
      }),
    );
  };

  static hidePost = (post: any) => async (dispatch: Dispatch) => {
    try {
      const postController = new DataController('Posts');
      var cusId = store.getState().customer.data?.Id;

      if (!cusId) {
        console.error('User not authenticated');
        return false;
      }
      // Kiểm tra quyền: chỉ người tạo bài đăng mới có thể ẩn
      if (post.CustomerId !== cusId) {
        // console.error('Not authorized to hide this post');
        showSnackbar({
          message: 'Bạn không có quyền ẩn bài đăng này',
          status: ComponentStatus.ERROR,
        });
        return false;
      }

      // Cập nhật trạng thái IsHidden của bài đăng
      const updateResult = await postController.edit([
        {
          ...post,
          IsHidden: true,
        },
      ]);
      if (updateResult.code === 200) {
        // Cập nhật state trong Redux
        dispatch(
          handleActions({
            type: HIDE_POST,
            postId: post.Id,
          }),
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error hiding post:', error);
      return false;
    }
  };

  //tạo action xóa post
  static deletePost = (post: any) => async (dispatch: Dispatch) => {
    try {
      const postController = new DataController('Posts');
      var cusId = store.getState().customer.data?.Id;
      if (!cusId) {
        console.error('User not authenticated');
        return false;
      }
      // Kiểm tra quyền: chỉ người tạo bài đăng mới có thể xóa
      // const post = await postController.getById(postId);
      if (!post || post.CustomerId !== cusId) {
        // console.error('Not authorized to delete this post');
        showSnackbar({
          message: 'Bạn không có quyền xóa bài đăng này',
          status: ComponentStatus.ERROR,
        });
        return false;
      }
      const deleteResult = await postController.delete([post.Id]);
      if (deleteResult.code === 200) {
        dispatch(
          handleActions({
            type: HIDE_POST,
            postId: post.Id,
          }),
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting post:', error);
      return false;
    }
  };

  static hidePostNocall = (PostId: string) => async (dispatch: Dispatch) => {
    dispatch(
      handleActions({
        type: HIDE_POST,
        postId: PostId,
      }),
    );
  };
  static updatePost = (post: any) => async (dispatch: Dispatch) => {
    try {
      // Dispatch action để cập nhật post trong Redux store
      dispatch(
        handleActions({
          type: UPDATE_POST,
          post: post,
        }),
      );

      return post;
    } catch (error) {
      console.error('Error updating post in Redux store:', error);
      return null;
    }
  };
}

// Helper functions
export async function getPostsForUser(
  cusId: string | null,
  page: number,
  size: number,
  search?: string,
) {
  var querySearch = `@Name: (*${search}*) `;
  const Postcontroller = new DataController('Posts');
  if (!cusId) {
    return Postcontroller.getListSimple({
      page,
      size,
      query: search
        ? querySearch + `@CustomerId:{${ConfigAPI.adminITM}} -@IsHidden:{true}`
        : `@CustomerId:{${ConfigAPI.adminITM}} -@IsHidden:{true}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
  }

  const CustomerController = new DataController('Customer');
  const cus = await CustomerController.getById(cusId);

  if (cus?.code !== 200) {
    return null;
  }
  // Lấy danh sách Followers của người dùng
  var listFollower = cus.data.Followers ? cus.data.Followers.split(',') : [];
  var lstId = [...listFollower, ConfigAPI.adminITM, cusId];
  //lấy danh sách bài post của danh sách group mà user đã tham gia
  var listGroup = [];
  const groupDA = new GroupDA();
  const joinedGroups = await groupDA.getJoinedGroups();
  if (joinedGroups?.code === 200) {
    listGroup = joinedGroups.data.map((item: any) => item.Id);
  }
  var queryGroup = ` | @GroupId:{${listGroup.join(' | ')}}`;
  const query = search
    ? querySearch +
      `@CustomerId:{${lstId.join(' | ')}} -@IsHidden:{true} ${
        listGroup.length > 0 ? queryGroup : ''
      }`
    : `@CustomerId:{${lstId.join(' | ')}} -@IsHidden:{true} ${
        listGroup.length > 0 ? queryGroup : ''
      }`;

  return Postcontroller.getListSimple({
    page,
    size,
    query,
    sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
  });
}
async function getPostsForUserFollowing(
  cusId: string | null,
  page: number,
  size: number,
  search?: string,
) {
  var querySearch = `@Name: (*${search}*) `;
  const Postcontroller = new DataController('Posts');
  if (!cusId) {
    return [];
  }
  // Lấy danh sách Followers của người dùng
  //lấy danh sách bài post của danh sách group mà user đã tham gia
  var listGroup = [];
  const groupDA = new GroupDA();
  const joinedGroups = await groupDA.getJoinedGroups();
  if (joinedGroups?.code === 200) {
    listGroup = joinedGroups.data.map((item: any) => item.Id);
  }
  var queryGroup = ` | @GroupId:{${listGroup.join(' | ')}}`;
  const query = search
    ? querySearch +
      `-@IsHidden:{true} ${listGroup.length > 0 ? queryGroup : ''}`
    : `-@IsHidden:{true} ${listGroup.length > 0 ? queryGroup : ''}`;

  return Postcontroller.getListSimple({
    page,
    size,
    query,
    sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
  });
}

async function enrichPostsWithData(posts: any[], cusId: string | null) {
  // Get all unique customer IDs and post IDs
  const customerIds = [...new Set(posts.map(post => post.CustomerId))];
  const groupIds = [
    ...new Set(
      posts
        .filter(
          (item: any) =>
            item.GroupId !== null &&
            item.GroupId !== undefined &&
            item.GroupId !== '',
        )
        .map(post => post.GroupId),
    ),
  ];
  const postIds = posts.map(post => post.Id);

  // Fetch all data in parallel
  const [customers, likes, comments, bookmarks, lstGroups] = await Promise.all([
    fetchCustomers(customerIds),
    fetchLikes(postIds),
    fetchComments(postIds),
    fetchBookmarks(postIds),
    fetchGroups(groupIds),
  ]);
  // Enrich posts with fetched data
  return posts.map(post => {
    const customer = customers.find((c: any) => c.Id === post.CustomerId);
    const group = lstGroups.find((c: any) => c.Id === post.GroupId);
    const postLikes = likes.filter((l: any) => l.PostsId === post.Id);
    const commentCount =
      comments.find((c: any) => c.PostsId === post.Id)?.CommentsCount || 0;
    const isBookmarked = bookmarks.some((b: any) => b.PostsId === post.Id);

    return {
      ...post,
      Likes: postLikes.length,
      IsLike: cusId
        ? postLikes.some((like: any) => like.CustomerId === cusId)
        : false,
      IsBookmark: isBookmarked,
      Comment: commentCount,
      relativeUser: post.GroupId
        ? {
            image: group?.Thumb,
            title: group?.Name,
            subtitle:
              customer?.Name + ' - ' + Ultis.getDiffrentTime(post.DateCreated),
          }
        : customer
        ? {
            image: customer.AvatarUrl,
            title: customer.Name,
            subtitle: Ultis.getDiffrentTime(post.DateCreated),
          }
        : null,
    };
  });
}

async function enrichPostsWithDataPopular(posts: any[]) {
  // Get all unique customer IDs and post IDs
  const customerIds = [...new Set(posts.map(post => post.CustomerId))];
  const postIds = posts.map(post => post.Id);
  const groupIds = [
    ...new Set(
      posts
        .filter(
          (item: any) =>
            item.GroupId !== null &&
            item.GroupId !== undefined &&
            item.GroupId !== '',
        )
        .map(post => post.GroupId),
    ),
  ];
  // Fetch all data in parallel
  const [customers, bookmarks] = await Promise.all([
    fetchCustomers(customerIds),
    // fetchComments(postIds),
    fetchBookmarks(postIds),
  ]);
  const lstGroups = await fetchGroups(groupIds);
  // Enrich posts with fetched data
  return posts.map(post => {
    const customer = customers.find((c: any) => c.Id === post.CustomerId);
    const isBookmarked = bookmarks.some((b: any) => b.PostsId === post.Id);
    const group = lstGroups.find((c: any) => c.Id === post.GroupId);
    return {
      ...post,
      IsBookmark: isBookmarked,
      relativeUser: post.GroupId
        ? {
            image: group?.Thumb,
            title: group?.Name,
            subtitle:
              customer?.Name + ' - ' + Ultis.getDiffrentTime(post.DateCreated),
          }
        : customer
        ? {
            image: customer.AvatarUrl,
            title: customer.Name,
            subtitle: Ultis.getDiffrentTime(post.DateCreated),
          }
        : null,
      // relativeUser: customer
      //   ? {
      //       image: customer.AvatarUrl,
      //       title: customer.Name,
      //       subtitle: Ultis.getDiffrentTime(post.DateCreated),
      //     }
      //   : null,
    };
  });
}

async function fetchCustomers(customerIds: string[]) {
  const customerController = new DataController('Customer');
  const response = await customerController.getListSimple({
    query: `@Id:{${customerIds.join(' | ')}}`,
  });
  return response.code === 200 ? response.data : [];
}

async function fetchLikes(postIds: string[]) {
  const likeController = new DataController('Likes');
  const response = await likeController.getListSimple({
    query: `@PostsId:{${postIds.join(' | ')}} `,
  });
  return response.code === 200 ? response.data : [];
}
async function fetchGroups(groupIds: string[]) {
  const groupController = new DataController('Group');
  const response = await groupController.getListSimple({
    query: `@Id:{${groupIds.join(' | ')}}`,
  });
  return response.code === 200 ? response.data : [];
}
async function fetchComments(postIds: string[]) {
  const commentController = new DataController('Comments');
  const response = await commentController.group({
    searchRaw: `@PostsId:{${postIds.join(' | ')}}`,
    reducers: 'LOAD * GROUPBY 1 @PostsId REDUCE COUNT 0 AS CommentsCount',
  });
  return response.code === 200 ? response.data : [];
}

async function fetchBookmarks(postIds: string[]) {
  const bookmarkController = new DataController('Post_Bookmark');
  var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
  const response = await bookmarkController.getListSimple({
    query: `@PostsId:{${postIds.join(' | ')}} @CustomerId: {${cusId}}`,
  });
  return response.code === 200 ? response.data : [];
}
